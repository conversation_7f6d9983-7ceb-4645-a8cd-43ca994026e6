<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Edit Show</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo BASE_URL; ?>/coordinator/editShowImages/<?php echo $id; ?>" class="btn btn-success me-2">
                    <i class="fas fa-images me-2"></i> Manage Images
                </a>
                <a href="javascript:history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Show Details</h5>
            <small class="text-muted">Using template: <?php echo isset($formTemplate) ? $formTemplate->name : 'Default Show Admin Form'; ?></small>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/coordinator/editShow/<?php echo $id; ?>" method="post" id="editShowForm" novalidate>
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="id" value="<?php echo $id; ?>">
                
                <?php
                // Debug information (only visible to admins)
                if ($_SESSION['user_role'] === 'admin' && empty($formFields)) {
                    echo '<div class="alert alert-warning">';
                    echo '<h5>Debug Information</h5>';
                    echo '<p>No form fields found in the template. This could be due to:</p>';
                    echo '<ul>';
                    echo '<li>The template does not exist in the database</li>';
                    echo '<li>The template exists but has no fields defined</li>';
                    echo '<li>There was an error decoding the JSON fields</li>';
                    echo '</ul>';
                    
                    if (isset($formTemplate)) {
                        echo '<p>Template information:</p>';
                        echo '<pre>' . print_r($formTemplate, true) . '</pre>';
                    } else {
                        echo '<p>No template object found.</p>';
                    }
                    
                    echo '</div>';
                }
                
                // Check if we have form fields from the template
                if (isset($formFields) && is_array($formFields)) {
                    // Group fields by row
                    $rows = [];
                    foreach ($formFields as $field) {
                        // Skip if field doesn't have a name or id
                        if (!isset($field['name']) && !isset($field['id'])) {
                            continue;
                        }
                        
                        $rowIndex = isset($field['row']) ? $field['row'] : 0;
                        if (!isset($rows[$rowIndex])) {
                            $rows[$rowIndex] = [];
                        }
                        $rows[$rowIndex][] = $field;
                    }
                    
                    // Sort rows by index
                    ksort($rows);
                    
                    // Check if we have any rows to render
                    if (empty($rows)) {
                        echo '<div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No form fields found in the template. Please 
                            <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                            to add fields.
                        </div>';
                    }
                    
                    // Render fields by row
                    foreach ($rows as $rowIndex => $rowFields) {
                        echo '<div class="row mb-3">';
                        
                        foreach ($rowFields as $field) {
                            // Skip section dividers - they're just for visual organization
                            if ($field['type'] === 'section') {
                                echo '<div class="col-12">';
                                echo '<hr class="mt-3 mb-3">';
                                // Label is hidden for separator fields to match design requirements
                                if (isset($field['help']) && !empty($field['help'])) {
                                    echo '<div class="form-text text-center mb-3">' . htmlspecialchars($field['help']) . '</div>';
                                } elseif (isset($field['placeholder']) && !empty($field['placeholder'])) {
                                    echo '<div class="form-text text-center mb-3">' . htmlspecialchars($field['placeholder']) . '</div>';
                                }
                                echo '</div>';
                                continue;
                            }
                            
                            // Get field value from the show object
                            if (!isset($field['name']) && !isset($field['id'])) {
                                // Skip fields without a name or id
                                echo '<div class="alert alert-warning">Field is missing both name and id attributes</div>';
                                continue;
                            }
                            
                            // Use name if available, otherwise use id
                            $fieldName = isset($field['name']) ? $field['name'] : $field['id'];
                            
                            // Get field width class
                            $widthClass = isset($field['width']) ? $field['width'] : 'col-md-12';
                            
                            // Get field value
                            $fieldValue = '';
                            
                            // Debug for all fields to help troubleshoot
                            error_log("Processing field: {$fieldName}");
                            
                            // First check if we have the value directly in the data array
                            // This is the most direct way to access the data
                            if (isset($data[$fieldName])) {
                                $fieldValue = $data[$fieldName];
                                error_log("  Value from data array directly: " . var_export($fieldValue, true));
                            }
                            // Then check if we have the value as a variable in the current scope
                            elseif (isset($$fieldName)) {
                                $fieldValue = $$fieldName;
                                error_log("  Value from variable in scope: " . var_export($fieldValue, true));
                            } 
                            // Then check if it's in the show object
                            elseif (isset($show->$fieldName)) {
                                $fieldValue = $show->$fieldName;
                                error_log("  Value from show object: " . var_export($fieldValue, true));
                            }
                            // Finally, use the default value from the field if available
                            elseif (isset($field['default'])) {
                                $fieldValue = $field['default'];
                                error_log("  Using default value: " . var_export($fieldValue, true));
                            } else {
                                error_log("  No value found for field: {$fieldName}");
                            }
                            
                            // Special handling for boolean fields
                            if (in_array($fieldName, ['fan_voting_enabled', 'is_free'])) {
                                // Ensure it's a proper boolean value
                                $fieldValue = filter_var($fieldValue, FILTER_VALIDATE_BOOLEAN);
                                error_log("  After boolean conversion: " . var_export($fieldValue, true));
                            }
                            
                            // Get error message if any
                            $errorVar = $fieldName . '_err';
                            $errorMsg = isset($$errorVar) ? $$errorVar : '';
                            
                            echo '<div class="' . $widthClass . '">';
                            
                            // Check if field type is defined
                            if (!isset($field['type'])) {
                                echo '<div class="alert alert-warning">Field is missing a type attribute</div>';
                                continue;
                            }
                            
                            // Skip listing_fee field for coordinators - they cannot modify it
                            if ($fieldName === 'listing_fee') {
                                echo '</div>'; // Close the column div
                                continue;
                            }
                            
                            // Handle coordinator_id field specially for coordinators - show read-only info
                            if ($fieldName === 'coordinator_id') {
                                $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                echo '<label class="form-label">' . htmlspecialchars($fieldLabel) . '</label>';
                                
                                // Add a hidden field to maintain the current coordinator
                                echo '<input type="hidden" name="coordinator_id" value="' . htmlspecialchars($fieldValue) . '">';
                                
                                // Show read-only information about the coordinator
                                if (isset($coordinators)) {
                                    foreach ($coordinators as $coordinator) {
                                        if ((string)$fieldValue === (string)$coordinator->id) {
                                            echo '<div class="form-control-plaintext">';
                                            echo '<strong>Coordinator:</strong> ' . htmlspecialchars($coordinator->name) . ' (' . htmlspecialchars($coordinator->email) . ')';
                                            echo '</div>';
                                            break;
                                        }
                                    }
                                } else {
                                    echo '<div class="form-control-plaintext">';
                                    echo '<strong>Coordinator ID:</strong> ' . htmlspecialchars($fieldValue);
                                    echo '</div>';
                                }
                                
                                if (!empty($field['help'])) {
                                    echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                }
                                
                                echo '</div>'; // Close the column div
                                continue;
                            }
                            
                            // Render field based on type
                            switch ($field['type']) {
                                case 'text':
                                case 'email':
                                case 'tel':
                                case 'url':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    
                                    // Special handling for location (venue) field
                                    if ($fieldName === 'location') {
                                        echo '<label for="venue_search" class="form-label">' . htmlspecialchars($fieldLabel);
                                        if (isset($field['required']) && $field['required']) {
                                            echo ' <span class="text-danger">*</span>';
                                        }
                                        echo '</label>';
                                        echo '<div class="mb-2">';
                                        echo '<div class="input-group">';
                                        echo '<input type="text" class="form-control" id="venue_search" placeholder="Start typing to search venues..." autocomplete="off">';
                                        echo '<button class="btn btn-outline-secondary" type="button" id="clearVenueBtn">';
                                        echo '<i class="fas fa-times"></i>';
                                        echo '</button>';
                                        echo '</div>';
                                        echo '<div id="venue_search_results" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto;"></div>';
                                        echo '<div class="form-text">Type at least 3 characters to search venues. Select one venue for your show.</div>';
                                        echo '</div>';
                                        
                                        // Hidden field to store the selected venue ID
                                        echo '<input type="hidden" id="venue_id" name="venue_id" value="">';
                                        
                                        // Hidden field for the location name (for backward compatibility)
                                        echo '<input type="hidden" class="form-control ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" ';
                                        echo 'id="' . $fieldName . '" name="' . $fieldName . '" ';
                                        echo 'value="' . htmlspecialchars($fieldValue) . '"';
                                        if (isset($field['required']) && $field['required']) {
                                            echo ' required';
                                        }
                                        echo '>';
                                        
                                        // Display selected venue information
                                        echo '<div id="selected_venue_info" class="card p-2 mb-2 ' . (empty($fieldValue) ? 'd-none' : '') . '">';
                                        echo '<div class="d-flex justify-content-between">';
                                        echo '<div>';
                                        echo '<strong id="selected_venue_name">' . htmlspecialchars($fieldValue) . '</strong>';
                                        echo '<div id="selected_venue_address" class="small text-muted"></div>';
                                        echo '</div>';
                                        echo '<button type="button" class="btn btn-sm btn-outline-danger" id="removeVenueBtn">';
                                        echo '<i class="fas fa-trash-alt"></i>';
                                        echo '</button>';
                                        echo '</div>';
                                        echo '</div>';
                                        
                                        echo '<div class="mt-2">';
                                        echo '<button type="button" class="btn btn-sm btn-outline-primary" id="createVenueModalBtn">';
                                        echo '<i class="fas fa-plus-circle"></i> Create New Venue';
                                        echo '</button>';
                                        echo '</div>';
                                        
                                        if (!empty($errorMsg)) {
                                            echo '<div class="invalid-feedback d-block">' . $errorMsg . '</div>';
                                        }
                                    }
                                    // Special handling for club field
                                    elseif ($fieldName === 'club') {
                                        echo '<label for="club_search" class="form-label">' . htmlspecialchars($fieldLabel);
                                        if (isset($field['required']) && $field['required']) {
                                            echo ' <span class="text-danger">*</span>';
                                        }
                                        echo '</label>';
                                        echo '<div id="club-search-container-' . $fieldName . '">';
                                        echo '<!-- Club search interface will be inserted here by JavaScript -->';
                                        echo '</div>';
                                        echo '<div class="form-text">Search and select a club to associate with this show.</div>';
                                        
                                        // Hidden field for the club ID
                                        echo '<input type="hidden" id="' . $fieldName . '" name="' . $fieldName . '" value="' . htmlspecialchars($fieldValue) . '"';
                                        if (isset($field['required']) && $field['required']) {
                                            echo ' required';
                                        }
                                        echo '>';
                                        
                                        if (!empty($errorMsg)) {
                                            echo '<div class="invalid-feedback d-block">' . $errorMsg . '</div>';
                                        }
                                    }
                                    // Regular text input for other fields
                                    else {
                                        echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) . 
                                             (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                        echo '<input type="' . $field['type'] . '" class="form-control ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName . 
                                             '" name="' . $fieldName . '" value="' . htmlspecialchars($fieldValue) . '"' . 
                                             (isset($field['required']) && $field['required'] ? ' required' : '') . '>';
                                        if (!empty($errorMsg)) {
                                            echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                        }
                                        if (!empty($field['help'])) {
                                            echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                        }
                                    }
                                    break;
                                    
                                case 'textarea':
                                case 'richtext':
                                    // Debug logging for textarea/richtext field
                                    error_log("Edit Template: Processing textarea/richtext field: {$fieldName}");
                                    error_log("Edit Template: Field value length: " . strlen((string)$fieldValue));
                                    
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) . 
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    
                                    // Add a hidden field to ensure the field is always included in the form submission
                                    // This prevents the field from being lost if it's empty
                                    echo '<input type="hidden" name="_has_' . $fieldName . '" value="1">';
                                    
                                    echo '<textarea class="form-control ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName . 
                                         '" name="' . $fieldName . '" rows="5"' . 
                                         (isset($field['required']) && $field['required'] ? ' required' : '') . '>' . 
                                         htmlspecialchars($fieldValue) . '</textarea>';
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'date':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;

                                    echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) .
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    echo '<input type="date" class="form-control ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName .
                                         '" name="' . $fieldName . '" value="' . htmlspecialchars($fieldValue) . '"' .
                                         (isset($field['required']) && $field['required'] ? ' required' : '') . '>';
                                    
                                    // Add timezone info for date fields
                                    if (in_array($fieldName, ['start_date', 'end_date', 'registration_start', 'registration_end'])) {
                                        $userTimezone = getUserTimezone($_SESSION['user_id'] ?? null);
                                        $timezoneAbbr = getTimezoneAbbreviation($userTimezone);
                                        echo '<div class="form-text">';
                                        echo '<i class="fas fa-clock me-1"></i>Times are shown in your timezone (' . $timezoneAbbr . ')';
                                        echo '</div>';
                                    }
                                    
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'select':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) . 
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    echo '<select class="form-select ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName . 
                                         '" name="' . $fieldName . '"' . 
                                         (isset($field['required']) && $field['required'] ? ' required' : '') . '>';
                                    
                                    // Add placeholder option if not required
                                    if (!isset($field['required']) || !$field['required']) {
                                        echo '<option value="">Select ' . htmlspecialchars($fieldLabel) . '</option>';
                                    }
                                    // Handle status field specially
                                    elseif ($fieldName === 'status') {
                                        // Debug logging for status field
                                        error_log("Edit Template: Processing status field with value: " . var_export($fieldValue, true));
                                        
                                        // Check if listing fee has been paid
                                        $listingPaid = isset($show->listing_paid) ? (bool)$show->listing_paid : false;
                                        
                                        // Special case for status select
                                        $statusOptions = [
                                            'draft' => 'Draft',
                                            'published' => 'Published',
                                            'cancelled' => 'Cancelled',
                                            'completed' => 'Completed'
                                        ];
                                        
                                        // If listing fee is not paid, disable the field and show only draft status
                                        if (!$listingPaid) {
                                            echo '<option value="draft" selected>Draft (Listing fee payment required)</option>';
                                            // Add a hidden field to ensure draft status is maintained
                                            echo '</select>';
                                            echo '<input type="hidden" name="status" value="draft">';
                                            echo '<div class="form-text text-warning">';
                                            echo '<i class="fas fa-exclamation-triangle me-1"></i>';
                                            echo 'Status cannot be changed until the listing fee has been paid.';
                                            echo '</div>';
                                            // Disable the select field
                                            echo '<script>document.getElementById("status").disabled = true;</script>';
                                        } else {
                                            foreach ($statusOptions as $value => $label) {
                                                $selected = (strtolower((string)$fieldValue) === strtolower((string)$value)) ? 'selected' : '';
                                                echo '<option value="' . $value . '" ' . $selected . '>' . $label . '</option>';
                                            }
                                        }
                                    }
                                    // Regular select fields
                                    else {
                                        // Debug logging for select field
                                        error_log("Edit Template: Processing select field: {$fieldName}");
                                        error_log("Edit Template: Field value: " . var_export($fieldValue, true));
                                        if (isset($field['options'])) {
                                            error_log("Edit Template: Options type: " . gettype($field['options']));
                                        } else {
                                            error_log("Edit Template: No options defined for select field");
                                        }
                                        
                                        // Add options
                                        if (isset($field['options'])) {
                                            // Check if options is an array
                                            if (is_array($field['options'])) {
                                                foreach ($field['options'] as $option) {
                                                    if (is_array($option)) {
                                                        $optionValue = isset($option['value']) ? $option['value'] : '';
                                                        $optionLabel = isset($option['label']) ? $option['label'] : $optionValue;
                                                    } else {
                                                        $optionValue = $option;
                                                        $optionLabel = $option;
                                                    }
                                                    
                                                    $selected = ($fieldValue == $optionValue) ? 'selected' : '';
                                                    
                                                    echo '<option value="' . htmlspecialchars($optionValue) . '" ' . $selected . '>' . 
                                                         htmlspecialchars($optionLabel) . '</option>';
                                                }
                                            }
                                            // If options is a string, try to parse it as JSON
                                            else if (is_string($field['options'])) {
                                                $parsedOptions = json_decode($field['options'], true);
                                                
                                                // If parsing succeeded and we have an array
                                                if (is_array($parsedOptions)) {
                                                    foreach ($parsedOptions as $option) {
                                                        if (is_array($option)) {
                                                            $optionValue = isset($option['value']) ? $option['value'] : '';
                                                            $optionLabel = isset($option['label']) ? $option['label'] : $optionValue;
                                                        } else {
                                                            $optionValue = $option;
                                                            $optionLabel = $option;
                                                        }
                                                        
                                                        $selected = ($fieldValue == $optionValue) ? 'selected' : '';
                                                        
                                                        echo '<option value="' . htmlspecialchars($optionValue) . '" ' . $selected . '>' . 
                                                             htmlspecialchars($optionLabel) . '</option>';
                                                    }
                                                }
                                                // If it's a string but not valid JSON, try to split by newlines
                                                else {
                                                    $lines = explode("\n", trim($field['options']));
                                                    foreach ($lines as $line) {
                                                        $line = trim($line);
                                                        if (!empty($line)) {
                                                            $selected = ($fieldValue == $line) ? 'selected' : '';
                                                            echo '<option value="' . htmlspecialchars($line) . '" ' . $selected . '>' . 
                                                                 htmlspecialchars($line) . '</option>';
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    
                                    // Add debug output for status field if needed
                                    if ($fieldName === 'status' && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
                                        echo '<div class="alert alert-info mb-2">';
                                        echo '<strong>Status Field Debug:</strong><br>';
                                        echo 'Field value: "' . htmlspecialchars($fieldValue) . '"<br>';
                                        echo 'Field value (hex): ' . bin2hex($fieldValue) . '<br>';
                                        echo 'Field value length: ' . strlen($fieldValue) . '<br>';
                                        
                                        if (isset($show->status)) {
                                            echo 'Show->status: "' . htmlspecialchars($show->status) . '"<br>';
                                            echo 'Show->status (hex): ' . bin2hex($show->status) . '<br>';
                                            echo 'Show->status length: ' . strlen($show->status) . '<br>';
                                        }
                                        
                                        if (isset($data['status'])) {
                                            echo 'Data[status]: "' . htmlspecialchars($data['status']) . '"<br>';
                                            echo 'Data[status] (hex): ' . bin2hex($data['status']) . '<br>';
                                            echo 'Data[status] length: ' . strlen($data['status']) . '<br>';
                                        }
                                        
                                        // Show comparison results for each option
                                        echo '<hr><strong>Comparison Results:</strong><br>';
                                        foreach (['draft', 'published', 'cancelled', 'completed'] as $opt) {
                                            echo 'Option "' . $opt . '": ';
                                            echo 'Strict equality: ' . ((string)$fieldValue === (string)$opt ? 'true' : 'false') . ', ';
                                            echo 'Case-insensitive: ' . (strtolower((string)$fieldValue) === strtolower((string)$opt) ? 'true' : 'false') . '<br>';
                                        }
                                        echo '</div>';
                                    } elseif ($fieldName === 'fan_voting_enabled') {
                                        // Special case for fan voting select
                                        // Debug the field value to help troubleshoot
                                        error_log("fan_voting_enabled field value: " . var_export($fieldValue, true));
                                        
                                        // Convert to boolean for comparison
                                        $fieldValueBool = filter_var($fieldValue, FILTER_VALIDATE_BOOLEAN);
                                        error_log("fan_voting_enabled as boolean: " . var_export($fieldValueBool, true));
                                        
                                        // Check all possible sources for this value
                                        if (isset($data['fan_voting_enabled'])) {
                                            error_log("fan_voting_enabled from data: " . var_export($data['fan_voting_enabled'], true));
                                        }
                                        if (isset($show->fan_voting_enabled)) {
                                            error_log("fan_voting_enabled from show: " . var_export($show->fan_voting_enabled, true));
                                        }
                                        
                                        echo '<option value="1" ' . ($fieldValueBool ? 'selected' : '') . '>Enabled</option>';
                                        echo '<option value="0" ' . ($fieldValueBool === false ? 'selected' : '') . '>Disabled</option>';
                                    }
                                    
                                    echo '</select>';
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'checkbox':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    
                                    // Debug for all checkbox fields
                                    error_log("Checkbox field: {$fieldName}, Value: " . var_export($fieldValue, true));
                                    
                                    // Check if this is a checkbox group (has options)
                                    if (isset($field['options']) && !empty($field['options'])) {
                                        // This is a checkbox group
                                        error_log("Edit Template: Processing checkbox group: {$fieldName}");
                                        
                                        echo '<label class="form-label">' . htmlspecialchars($fieldLabel) . 
                                             (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                        
                                        // Try to parse the field value as JSON if it's a string
                                        $selectedValues = [];
                                        if (is_string($fieldValue) && !empty($fieldValue)) {
                                            $jsonDecoded = json_decode($fieldValue, true);
                                            if (is_array($jsonDecoded)) {
                                                $selectedValues = $jsonDecoded;
                                                error_log("Edit Template: Parsed JSON values: " . json_encode($selectedValues));
                                            } else {
                                                // If not JSON, it might be a single value
                                                $selectedValues = [$fieldValue];
                                                error_log("Edit Template: Using single value: " . json_encode($selectedValues));
                                            }
                                        } elseif (is_array($fieldValue)) {
                                            $selectedValues = $fieldValue;
                                            error_log("Edit Template: Using array values directly: " . json_encode($selectedValues));
                                        }
                                        
                                        // Add a hidden field to ensure the field is always included in the form submission
                                        echo '<input type="hidden" name="_has_' . $fieldName . '" value="1">';
                                        
                                        // Add a hidden field to store the original values (for reference)
                                        echo '<input type="hidden" name="' . $fieldName . '_original" value="' . htmlspecialchars(is_string($fieldValue) ? $fieldValue : json_encode($fieldValue)) . '">';
                                        
                                        // Add a hidden field to track that this is a checkbox group
                                        echo '<input type="hidden" name="_is_checkbox_group_' . $fieldName . '" value="1">';
                                        
                                        echo '<div class="checkbox-group mb-3">';
                                        
                                        // Handle different types of options
                                        if (is_array($field['options'])) {
                                            // Array of options
                                            $index = 0;
                                            foreach ($field['options'] as $option) {
                                                if (is_array($option)) {
                                                    $optionValue = isset($option['value']) ? $option['value'] : '';
                                                    $optionLabel = isset($option['label']) ? $option['label'] : $optionValue;
                                                } else {
                                                    $optionValue = $option;
                                                    $optionLabel = $option;
                                                }
                                                
                                                // Check if this option is selected
                                                $isChecked = in_array($optionValue, $selectedValues);
                                                
                                                echo '<div class="form-check">';
                                                // Use a consistent naming convention that doesn't use array notation
                                                echo '<input class="form-check-input" type="checkbox" id="' . $fieldName . '_' . $index . 
                                                     '" name="' . $fieldName . '_' . $optionValue . '" value="1"' . 
                                                     ($isChecked ? ' checked' : '') . '>';
                                                echo '<label class="form-check-label" for="' . $fieldName . '_' . $index . '">' . 
                                                     htmlspecialchars($optionLabel) . '</label>';
                                                echo '</div>';
                                                $index++;
                                            }
                                        } elseif (is_string($field['options'])) {
                                            // Try to parse as JSON
                                            $parsedOptions = json_decode($field['options'], true);
                                            
                                            if (is_array($parsedOptions)) {
                                                // JSON array of options
                                                $index = 0;
                                                foreach ($parsedOptions as $option) {
                                                    if (is_array($option)) {
                                                        $optionValue = isset($option['value']) ? $option['value'] : '';
                                                        $optionLabel = isset($option['label']) ? $option['label'] : $optionValue;
                                                    } else {
                                                        $optionValue = $option;
                                                        $optionLabel = $option;
                                                    }
                                                    
                                                    // Check if this option is selected
                                                    $isChecked = in_array($optionValue, $selectedValues);
                                                    
                                                    echo '<div class="form-check">';
                                                    echo '<input class="form-check-input" type="checkbox" id="' . $fieldName . '_' . $index . 
                                                         '" name="' . $fieldName . '_' . $optionValue . '" value="1"' . 
                                                         ($isChecked ? ' checked' : '') . '>';
                                                    echo '<label class="form-check-label" for="' . $fieldName . '_' . $index . '">' . 
                                                         htmlspecialchars($optionLabel) . '</label>';
                                                    echo '</div>';
                                                    $index++;
                                                }
                                            } else {
                                                // Try to split by newlines
                                                $lines = explode("\n", trim($field['options']));
                                                $index = 0;
                                                foreach ($lines as $line) {
                                                    $line = trim($line);
                                                    if (!empty($line)) {
                                                        // Check if this option is selected
                                                        $isChecked = in_array($line, $selectedValues);
                                                        
                                                        echo '<div class="form-check">';
                                                        echo '<input class="form-check-input" type="checkbox" id="' . $fieldName . '_' . $index . 
                                                             '" name="' . $fieldName . '_' . $line . '" value="1"' . 
                                                             ($isChecked ? ' checked' : '') . '>';
                                                        echo '<label class="form-check-label" for="' . $fieldName . '_' . $index . '">' . 
                                                             htmlspecialchars($line) . '</label>';
                                                        echo '</div>';
                                                        $index++;
                                                    }
                                                }
                                            }
                                        }
                                        
                                        echo '</div>';
                                    } else {
                                        // This is a single checkbox
                                        
                                        // Special handling for fan_voting_enabled field
                                        if ($fieldName === 'fan_voting_enabled') {
                                            // Get the value directly from the database to ensure accuracy
                                            try {
                                                $db = new Database();
                                                $db->query("SELECT fan_voting_enabled FROM shows WHERE id = :id");
                                                $db->bind(':id', $show->id);
                                                $db->execute();
                                                $result = $db->single();
                                                if ($result) {
                                                    $fieldValue = (int)$result->fan_voting_enabled;
                                                    error_log("fan_voting_enabled from direct DB query: " . var_export($fieldValue, true));
                                                }
                                            } catch (Exception $e) {
                                                error_log("Error getting fan_voting_enabled from DB: " . $e->getMessage());
                                            }
                                        }
                                        
                                        // Check all possible sources for this value
                                        if (isset($data[$fieldName])) {
                                            error_log("{$fieldName} from data: " . var_export($data[$fieldName], true));
                                        }
                                        if (isset($show->$fieldName)) {
                                            error_log("{$fieldName} from show: " . var_export($show->$fieldName, true));
                                        }
                                        
                                        // Convert to boolean for proper checkbox handling
                                        $isChecked = filter_var($fieldValue, FILTER_VALIDATE_BOOLEAN);
                                        error_log("{$fieldName} as boolean: " . var_export($isChecked, true));
                                        
                                        // Add a hidden field to ensure the field is always included in the form submission
                                        // This prevents the field from being lost if it's unchecked
                                        echo '<input type="hidden" name="_has_' . $fieldName . '" value="1">';
                                        
                                        echo '<div class="form-check mt-4">';
                                        echo '<input class="form-check-input" type="checkbox" id="' . $fieldName . '" name="' . $fieldName . 
                                             '" value="1"' . ($isChecked ? ' checked' : '') . '>';
                                        echo '<label class="form-check-label" for="' . $fieldName . '">' . 
                                             htmlspecialchars($fieldLabel) . '</label>';
                                        echo '</div>';
                                    }
                                    
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'number':
                                    // Skip listing_fee field for coordinators - they cannot modify it
                                    if ($fieldName === 'listing_fee') {
                                        break;
                                    }
                                    
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) . 
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    echo '<input type="number" class="form-control ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName . 
                                         '" name="' . $fieldName . '" value="' . htmlspecialchars($fieldValue) . '"' . 
                                         (isset($field['step']) ? ' step="' . $field['step'] . '"' : ' step="0.01"') . 
                                         (isset($field['min']) ? ' min="' . $field['min'] . '"' : ' min="0"') . 
                                         (isset($field['required']) && $field['required'] ? ' required' : '') . '>';
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'html':
                                    // Custom HTML content
                                    if (isset($field['content'])) {
                                        echo $field['content'];
                                    } elseif (isset($field['help'])) {
                                        echo '<div class="form-text">' . $field['help'] . '</div>';
                                    } else {
                                        echo '<div class="alert alert-info">Custom HTML content (empty)</div>';
                                    }
                                    break;
                                    
                                case 'hidden':
                                    // Skip listing_fee field for coordinators - they cannot modify it
                                    if ($fieldName === 'listing_fee') {
                                        break;
                                    }
                                    
                                    // Hidden field - just output the input with no label
                                    echo '<input type="hidden" id="' . $fieldName . '" name="' . $fieldName . 
                                         '" value="' . htmlspecialchars($fieldValue) . '">';
                                    // No need for error messages or help text for hidden fields
                                    break;
                                    
                                case 'time':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) . 
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    echo '<input type="time" class="form-control ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName . 
                                         '" name="' . $fieldName . '" value="' . htmlspecialchars($fieldValue) . '"' . 
                                         (isset($field['required']) && $field['required'] ? ' required' : '') . '>';
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'datetime':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    
                                    // The controller already provides the correct format for datetime-local (YYYY-MM-DDTHH:MM)
                                    $formattedValue = $fieldValue;
                                    
                                    echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) . 
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    echo '<input type="datetime-local" class="form-control ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName . 
                                         '" name="' . $fieldName . '" value="' . htmlspecialchars($formattedValue) . '"' . 
                                         (isset($field['required']) && $field['required'] ? ' required' : '') . '>';
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'color':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) . 
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    echo '<input type="color" class="form-control form-control-color ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName . 
                                         '" name="' . $fieldName . '" value="' . htmlspecialchars($fieldValue) . '"' . 
                                         (isset($field['required']) && $field['required'] ? ' required' : '') . '>';
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'radio':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    echo '<label class="form-label">' . htmlspecialchars($fieldLabel) . 
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    
                                    // Debug logging for radio field
                                    error_log("Edit Template: Processing radio field: {$fieldName}");
                                    error_log("Edit Template: Field value: " . var_export($fieldValue, true));
                                    if (isset($field['options'])) {
                                        error_log("Edit Template: Options type: " . gettype($field['options']));
                                    } else {
                                        error_log("Edit Template: No options defined for radio field");
                                    }
                                    
                                    // Handle options properly
                                    if (isset($field['options'])) {
                                        echo '<div class="mb-3">';
                                        
                                        // Check if options is an array
                                        if (is_array($field['options'])) {
                                            foreach ($field['options'] as $option) {
                                                if (is_array($option)) {
                                                    $optionValue = isset($option['value']) ? $option['value'] : '';
                                                    $optionLabel = isset($option['label']) ? $option['label'] : $optionValue;
                                                } else {
                                                    $optionValue = $option;
                                                    $optionLabel = $option;
                                                }
                                                
                                                $checked = ($fieldValue == $optionValue) ? 'checked' : '';
                                                
                                                echo '<div class="form-check">';
                                                echo '<input class="form-check-input" type="radio" name="' . $fieldName . '" id="' . $fieldName . '_' . $optionValue . 
                                                     '" value="' . htmlspecialchars($optionValue) . '" ' . $checked . '>';
                                                echo '<label class="form-check-label" for="' . $fieldName . '_' . $optionValue . '">' . 
                                                     htmlspecialchars($optionLabel) . '</label>';
                                                echo '</div>';
                                            }
                                        } 
                                        // If options is a string, try to parse it as JSON
                                        else if (is_string($field['options'])) {
                                            $parsedOptions = json_decode($field['options'], true);
                                            
                                            // If parsing succeeded and we have an array
                                            if (is_array($parsedOptions)) {
                                                foreach ($parsedOptions as $option) {
                                                    if (is_array($option)) {
                                                        $optionValue = isset($option['value']) ? $option['value'] : '';
                                                        $optionLabel = isset($option['label']) ? $option['label'] : $optionValue;
                                                    } else {
                                                        $optionValue = $option;
                                                        $optionLabel = $option;
                                                    }
                                                    
                                                    $checked = ($fieldValue == $optionValue) ? 'checked' : '';
                                                    
                                                    echo '<div class="form-check">';
                                                    echo '<input class="form-check-input" type="radio" name="' . $fieldName . '" id="' . $fieldName . '_' . $optionValue . 
                                                         '" value="' . htmlspecialchars($optionValue) . '" ' . $checked . '>';
                                                    echo '<label class="form-check-label" for="' . $fieldName . '_' . $optionValue . '">' . 
                                                         htmlspecialchars($optionLabel) . '</label>';
                                                    echo '</div>';
                                                }
                                            }
                                            // If it's a string but not valid JSON, create a single option
                                            else {
                                                $optionValue = $field['options'];
                                                $optionLabel = $field['options'];
                                                $checked = ($fieldValue == $optionValue) ? 'checked' : '';
                                                
                                                echo '<div class="form-check">';
                                                echo '<input class="form-check-input" type="radio" name="' . $fieldName . '" id="' . $fieldName . '_option" 
                                                     value="' . htmlspecialchars($optionValue) . '" ' . $checked . '>';
                                                echo '<label class="form-check-label" for="' . $fieldName . '_option">' . 
                                                     htmlspecialchars($optionLabel) . '</label>';
                                                echo '</div>';
                                            }
                                        }
                                        // For any other type, create a default option
                                        else {
                                            echo '<div class="form-check">';
                                            echo '<input class="form-check-input" type="radio" name="' . $fieldName . '" id="' . $fieldName . '_default" 
                                                 value="yes" ' . ($fieldValue == 'yes' ? 'checked' : '') . '>';
                                            echo '<label class="form-check-label" for="' . $fieldName . '_default">Yes</label>';
                                            echo '</div>';
                                            
                                            echo '<div class="form-check">';
                                            echo '<input class="form-check-input" type="radio" name="' . $fieldName . '" id="' . $fieldName . '_default_no" 
                                                 value="no" ' . ($fieldValue == 'no' ? 'checked' : '') . '>';
                                            echo '<label class="form-check-label" for="' . $fieldName . '_default_no">No</label>';
                                            echo '</div>';
                                        }
                                        
                                        echo '</div>';
                                    }
                                    // If no options are defined, create default Yes/No options
                                    else {
                                        echo '<div class="mb-3">';
                                        echo '<div class="form-check">';
                                        echo '<input class="form-check-input" type="radio" name="' . $fieldName . '" id="' . $fieldName . '_yes" 
                                             value="yes" ' . ($fieldValue == 'yes' ? 'checked' : '') . '>';
                                        echo '<label class="form-check-label" for="' . $fieldName . '_yes">Yes</label>';
                                        echo '</div>';
                                        
                                        echo '<div class="form-check">';
                                        echo '<input class="form-check-input" type="radio" name="' . $fieldName . '" id="' . $fieldName . '_no" 
                                             value="no" ' . ($fieldValue == 'no' ? 'checked' : '') . '>';
                                        echo '<label class="form-check-label" for="' . $fieldName . '_no">No</label>';
                                        echo '</div>';
                                        echo '</div>';
                                    }
                                    
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback d-block">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'file':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) . 
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    
                                    // Add a hidden field to ensure the field is always included in the form submission
                                    echo '<input type="hidden" name="_has_' . $fieldName . '" value="1">';
                                    
                                    echo '<input type="file" class="form-control ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName . 
                                         '" name="' . $fieldName . '"' . 
                                         (isset($field['required']) && $field['required'] ? ' required' : '') . '>';
                                    
                                    // Show current file if available
                                    if (!empty($fieldValue)) {
                                        echo '<div class="mt-2">';
                                        echo '<p>Current file: ' . htmlspecialchars($fieldValue) . '</p>';
                                        echo '</div>';
                                    }
                                    
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                case 'range':
                                    $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
                                    echo '<label for="' . $fieldName . '" class="form-label">' . htmlspecialchars($fieldLabel) . 
                                         (isset($field['required']) && $field['required'] ? ' <span class="text-danger">*</span>' : '') . '</label>';
                                    
                                    $min = isset($field['min']) ? $field['min'] : '0';
                                    $max = isset($field['max']) ? $field['max'] : '100';
                                    $step = isset($field['step']) ? $field['step'] : '1';
                                    
                                    echo '<div class="d-flex align-items-center">';
                                    echo '<input type="range" class="form-range ' . (!empty($errorMsg) ? 'is-invalid' : '') . '" id="' . $fieldName . 
                                         '" name="' . $fieldName . '" value="' . htmlspecialchars($fieldValue) . '"' . 
                                         ' min="' . $min . '" max="' . $max . '" step="' . $step . '"' .
                                         (isset($field['required']) && $field['required'] ? ' required' : '') . '>';
                                    echo '<span class="ms-2" id="' . $fieldName . '_value">' . htmlspecialchars($fieldValue) . '</span>';
                                    echo '</div>';
                                    
                                    // Add JavaScript to update the value display
                                    echo '<script>
                                        document.getElementById("' . $fieldName . '").addEventListener("input", function() {
                                            document.getElementById("' . $fieldName . '_value").textContent = this.value;
                                        });
                                    </script>';
                                    
                                    if (!empty($errorMsg)) {
                                        echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                    }
                                    if (!empty($field['help'])) {
                                        echo '<div class="form-text">' . htmlspecialchars($field['help']) . '</div>';
                                    }
                                    break;
                                    
                                default:
                                    echo '<p class="text-danger">Unknown field type: ' . htmlspecialchars($field['type']) . '</p>';
                            }
                            
                            echo '</div>';
                        }
                        
                        echo '</div>';
                    }
                }
                ?>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> After saving the show, you'll be able to upload and manage images using the Image Editor.
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Update Show
                    </button>
                </div>
            </form>
        </div>
    </div>
    

    
    <!-- Show Categories Section -->
    <div class="card mt-4" id="categories">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Categories</h5>
            <div class="btn-group">
                <a href="<?php echo BASE_URL; ?>/coordinator/addCategory/<?php echo $id; ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> Add Category
                </a>
                <form action="<?php echo BASE_URL; ?>/coordinator/addDefaultCategoriesToShow/<?php echo $id; ?>" method="post" class="d-inline">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-sm btn-success">
                        <i class="fas fa-list-alt me-1"></i> Add Default Categories
                    </button>
                </form>
                <?php if (!empty($categories)): ?>
                <button type="button" class="btn btn-sm btn-danger delete-all-categories-btn">
                    <i class="fas fa-trash me-1"></i> Delete All Categories
                </button>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <?php if (!empty($categories)) : ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Fee</th>
                                <th>Max Entries</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($categories as $category) : ?>
                                <tr>
                                    <td><?php echo $category->name; ?></td>
                                    <td><?php echo $category->description; ?></td>
                                    <td>$<?php echo number_format($category->registration_fee, 2); ?></td>
                                    <td><?php echo $category->max_entries ?: 'Unlimited'; ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo BASE_URL; ?>/coordinator/editCategory/<?php echo $category->id; ?>" class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-show-category-btn" 
                                                    data-id="<?php echo $category->id; ?>"
                                                    data-name="<?php echo htmlspecialchars($category->name, ENT_QUOTES); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else : ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No categories have been added yet. Click the "Add Category" button to create your first category.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Judging Metrics Section -->
    <div class="card mt-4" id="metrics">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Judging Metrics</h5>
            <div class="btn-group">
                <a href="<?php echo BASE_URL; ?>/coordinator/addMetric/<?php echo $id; ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> Add Metric
                </a>
                <form action="<?php echo BASE_URL; ?>/coordinator/addDefaultMetricsToShow/<?php echo $id; ?>" method="post" class="d-inline">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-sm btn-success">
                        <i class="fas fa-balance-scale me-1"></i> Add Default Metrics
                    </button>
                </form>
                <?php if (!empty($metrics)): ?>
                <button type="button" class="btn btn-sm btn-danger delete-all-metrics-btn">
                    <i class="fas fa-trash me-1"></i> Delete All Metrics
                </button>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <?php if (!empty($metrics)) : ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Weight</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($metrics as $metric) : ?>
                                <tr>
                                    <td><?php echo $metric->name; ?></td>
                                    <td><?php echo $metric->description; ?></td>
                                    <td><?php echo $metric->weight; ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo BASE_URL; ?>/coordinator/editMetric/<?php echo $metric->id; ?>" class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-show-metric-btn" 
                                                    data-id="<?php echo $metric->id; ?>"
                                                    data-name="<?php echo htmlspecialchars($metric->name, ENT_QUOTES); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else : ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No judging metrics have been added yet. Click the "Add Metric" button to create your first metric.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Age Weight Metrics Section -->
    <div class="card mt-4" id="age-weights">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Age Weight Metrics</h5>
            <div class="btn-group">
                <a href="<?php echo BASE_URL; ?>/coordinator/addAgeWeight/<?php echo $id; ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> Add Age Weight
                </a>
                <form action="<?php echo BASE_URL; ?>/coordinator/addDefaultAgeWeightsToShow/<?php echo $id; ?>" method="post" class="d-inline">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-sm btn-success">
                        <i class="fas fa-history me-1"></i> Add Default Age Weights
                    </button>
                </form>
                <?php if (!empty($ageWeights)): ?>
                <button type="button" class="btn btn-sm btn-danger delete-all-age-weights-btn">
                    <i class="fas fa-trash me-1"></i> Delete All Age Weights
                </button>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <?php if (!empty($ageWeights)) : ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Age Range</th>
                                <th>Multiplier</th>
                                <th>Description</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ageWeights as $ageWeight) : ?>
                                <tr>
                                    <td><?php echo $ageWeight->min_age; ?> - <?php echo $ageWeight->max_age; ?></td>
                                    <td><?php echo $ageWeight->multiplier; ?></td>
                                    <td><?php echo isset($ageWeight->description) ? $ageWeight->description : ''; ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/coordinator/editAgeWeight/<?php echo $ageWeight->id; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger delete-age-weight-btn" 
                                                data-id="<?php echo $ageWeight->id; ?>"
                                                data-min-year="<?php echo $ageWeight->min_age; ?>"
                                                data-max-year="<?php echo $ageWeight->max_age; ?>">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else : ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No age weights have been added yet. Click the "Setup Age Weights" button to configure age-based scoring, or "Add Default Age Weights" to use predefined weights.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add JavaScript for form interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form submission handler for custom validation
    const editShowForm = document.getElementById('editShowForm');
    if (editShowForm) {
        editShowForm.addEventListener('submit', function(e) {
            console.log('Form submission triggered');
            
            // Get datetime fields
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');
            const registrationStartInput = document.getElementById('registration_start');
            const registrationEndInput = document.getElementById('registration_end');
            
            let hasErrors = false;
            
            // Validate start and end dates
            if (startDateInput && endDateInput && startDateInput.value && endDateInput.value) {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);
                
                console.log('Validating dates:', {
                    start: startDateInput.value,
                    end: endDateInput.value,
                    startDate: startDate,
                    endDate: endDate,
                    endLessOrEqualStart: endDate <= startDate
                });
                
                if (endDate <= startDate) {
                    alert('End date must be after start date');
                    endDateInput.focus();
                    hasErrors = true;
                }
            }
            
            // Validate registration dates
            if (!hasErrors && registrationStartInput && registrationEndInput && registrationStartInput.value && registrationEndInput.value) {
                const regStartDate = new Date(registrationStartInput.value);
                const regEndDate = new Date(registrationEndInput.value);
                
                if (regEndDate <= regStartDate) {
                    alert('Registration end date must be after registration start date');
                    registrationEndInput.focus();
                    hasErrors = true;
                } else if (startDateInput && startDateInput.value) {
                    const eventStartDate = new Date(startDateInput.value);
                    if (regEndDate > eventStartDate) {
                        alert('Registration must end before the show starts');
                        registrationEndInput.focus();
                        hasErrors = true;
                    }
                }
            }
            
            if (hasErrors) {
                e.preventDefault();
                return false;
            }
            
            console.log('Form validation passed, submitting...');
            return true;
        });
    }
    // Handle is_free checkbox to disable/enable registration_fee field
    const isFreeCheckbox = document.getElementById('is_free');
    const registrationFeeField = document.getElementById('registration_fee');
    
    if (isFreeCheckbox && registrationFeeField) {
        // Initial state
        if (isFreeCheckbox.checked) {
            registrationFeeField.disabled = true;
            registrationFeeField.value = '0.00';
        }
        
        // Add event listener for changes
        isFreeCheckbox.addEventListener('change', function() {
            if (this.checked) {
                // If checked, disable fee field and set to 0
                registrationFeeField.disabled = true;
                registrationFeeField.value = '0.00';
                // Add a visual indicator that the field is disabled
                registrationFeeField.parentElement.classList.add('opacity-50');
            } else {
                // If unchecked, enable fee field
                registrationFeeField.disabled = false;
                registrationFeeField.parentElement.classList.remove('opacity-50');
            }
        });
        
        // Trigger the change event to set initial state
        isFreeCheckbox.dispatchEvent(new Event('change'));
    }
    
    // Handle category delete buttons
    const deleteCategoryButtons = document.querySelectorAll('.delete-show-category-btn');
    deleteCategoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            
            // Update the modal content
            document.getElementById('deleteCategoryMessage').textContent = 
                `Are you sure you want to delete the category "${name}"?`;
            
            // Update the form action
            document.getElementById('deleteCategoryForm').action = 
                `<?php echo BASE_URL; ?>/coordinator/deleteCategory/${id}`;
            
            // Show the modal with a slight delay to ensure Bootstrap is fully loaded
            setTimeout(() => {
                const modalElement = document.getElementById('deleteCategoryModal');
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            }, 50);
        });
    });
    
    // Handle metric delete buttons
    const deleteMetricButtons = document.querySelectorAll('.delete-show-metric-btn');
    deleteMetricButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            
            // Update the modal content
            document.getElementById('deleteMetricMessage').textContent = 
                `Are you sure you want to delete the metric "${name}"?`;
            
            // Update the form action
            document.getElementById('deleteMetricForm').action = 
                `<?php echo BASE_URL; ?>/coordinator/deleteMetric/${id}`;
            
            // Show the modal with a slight delay to ensure Bootstrap is fully loaded
            setTimeout(() => {
                const modalElement = document.getElementById('deleteMetricModal');
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            }, 50);
        });
    });
    
    // Handle age weight delete buttons
    const deleteAgeWeightButtons = document.querySelectorAll('.delete-age-weight-btn');
    deleteAgeWeightButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const minYear = this.getAttribute('data-min-year');
            const maxYear = this.getAttribute('data-max-year');
            
            // Update the modal content
            document.getElementById('deleteAgeWeightMessage').textContent = 
                `Are you sure you want to delete the age weight for ages ${minYear} - ${maxYear}?`;
            
            // Update the form action
            document.getElementById('deleteAgeWeightForm').action = 
                `<?php echo BASE_URL; ?>/coordinator/deleteAgeWeight/${id}`;
            
            // Show the modal with a slight delay to ensure Bootstrap is fully loaded
            setTimeout(() => {
                const modalElement = document.getElementById('deleteAgeWeightModal');
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            }, 50);
        });
    });
    
    // Handle delete all age weights button
    const deleteAllAgeWeightsButton = document.querySelector('.delete-all-age-weights-btn');
    if (deleteAllAgeWeightsButton) {
        deleteAllAgeWeightsButton.addEventListener('click', function() {
            // Show the modal with a slight delay to ensure Bootstrap is fully loaded
            setTimeout(() => {
                const modalElement = document.getElementById('deleteAllAgeWeightsModal');
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            }, 50);
        });
    }
    
    // Handle delete all categories button
    const deleteAllCategoriesButton = document.querySelector('.delete-all-categories-btn');
    if (deleteAllCategoriesButton) {
        deleteAllCategoriesButton.addEventListener('click', function() {
            // Show the modal with a slight delay to ensure Bootstrap is fully loaded
            setTimeout(() => {
                const modalElement = document.getElementById('deleteAllCategoriesModal');
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            }, 50);
        });
    }
    
    // Handle delete all metrics button
    const deleteAllMetricsButton = document.querySelector('.delete-all-metrics-btn');
    if (deleteAllMetricsButton) {
        deleteAllMetricsButton.addEventListener('click', function() {
            // Show the modal with a slight delay to ensure Bootstrap is fully loaded
            setTimeout(() => {
                const modalElement = document.getElementById('deleteAllMetricsModal');
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            }, 50);
        });
    }
});
</script>

<!-- Single Delete Modals -->
<!-- Category Delete Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteCategoryModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteCategoryMessage">Are you sure you want to delete this category?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteCategoryForm" action="" method="post">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Metric Delete Modal -->
<div class="modal fade" id="deleteMetricModal" tabindex="-1" aria-labelledby="deleteMetricModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteMetricModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteMetricMessage">Are you sure you want to delete this metric?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteMetricForm" action="" method="post">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Age Weight Delete Modal -->
<div class="modal fade" id="deleteAgeWeightModal" tabindex="-1" aria-labelledby="deleteAgeWeightModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAgeWeightModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteAgeWeightMessage">Are you sure you want to delete this age weight?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteAgeWeightForm" action="" method="post">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete All Age Weights Modal -->
<div class="modal fade" id="deleteAllAgeWeightsModal" tabindex="-1" aria-labelledby="deleteAllAgeWeightsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAllAgeWeightsModalLabel">Confirm Delete All</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong>ALL</strong> age weights for this show?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?php echo BASE_URL; ?>/coordinator/deleteAllAgeWeights/<?php echo $id; ?>" method="post">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-danger">Delete All Age Weights</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete All Categories Modal -->
<div class="modal fade" id="deleteAllCategoriesModal" tabindex="-1" aria-labelledby="deleteAllCategoriesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAllCategoriesModalLabel">Confirm Delete All</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong>ALL</strong> categories for this show?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?php echo BASE_URL; ?>/coordinator/deleteAllCategories/<?php echo $id; ?>" method="post">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-danger">Delete All Categories</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete All Metrics Modal -->
<div class="modal fade" id="deleteAllMetricsModal" tabindex="-1" aria-labelledby="deleteAllMetricsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAllMetricsModalLabel">Confirm Delete All</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong>ALL</strong> judging metrics for this show?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?php echo BASE_URL; ?>/coordinator/deleteAllMetrics/<?php echo $id; ?>" method="post">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <button type="submit" class="btn btn-danger">Delete All Metrics</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Venue and Club Search CSS and JS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/club-search.css">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/venue-modal.css">
<script src="<?php echo BASE_URL; ?>/public/js/club-search.js"></script>
<script src="<?php echo BASE_URL; ?>/public/js/venue-modal.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize venue search functionality
    initializeVenueSearch();
    
    // Initialize club search functionality
    initializeClubSearch();
    
    // Initialize venue modal
    initializeVenueModal();
});

function initializeVenueSearch() {
    const venueSearchInput = document.getElementById('venue_search');
    const venueSearchResults = document.getElementById('venue_search_results');
    const venueIdInput = document.getElementById('venue_id');
    const selectedVenueInfo = document.getElementById('selected_venue_info');
    const selectedVenueName = document.getElementById('selected_venue_name');
    const selectedVenueAddress = document.getElementById('selected_venue_address');
    const clearVenueBtn = document.getElementById('clearVenueBtn');
    const removeVenueBtn = document.getElementById('removeVenueBtn');
    const locationInput = document.getElementById('location');
    
    if (!venueSearchInput || !venueSearchResults) {
        return; // Elements not found, skip initialization
    }
    
    // Debounce function
    function debounce(func, wait) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }
    
    // Search venues as user types
    venueSearchInput.addEventListener('input', debounce(function() {
        const searchTerm = this.value.trim();
        
        if (searchTerm.length < 3) {
            venueSearchResults.innerHTML = '';
            venueSearchResults.classList.remove('show');
            return;
        }
        
        // Make AJAX request to search venues
        fetch('<?php echo BASE_URL; ?>/calendar/searchVenues', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': '<?php echo $csrf_token; ?>'
            },
            body: 'search=' + encodeURIComponent(searchTerm) + '&local_only=true'
        })
        .then(response => response.json())
        .then(data => {
            venueSearchResults.innerHTML = '';
            
            if (data.success && data.venues && data.venues.length > 0) {
                data.venues.forEach(venue => {
                    const resultItem = document.createElement('a');
                    resultItem.classList.add('dropdown-item');
                    resultItem.href = '#';
                    resultItem.innerHTML = `<strong>${venue.name}</strong>` + 
                        (venue.city && venue.state ? `<br><small>${venue.city}, ${venue.state}</small>` : '');
                    
                    resultItem.dataset.id = venue.id;
                    resultItem.dataset.name = venue.name;
                    resultItem.dataset.address = venue.address || '';
                    resultItem.dataset.city = venue.city || '';
                    resultItem.dataset.state = venue.state || '';
                    resultItem.dataset.zip = venue.zip || '';
                    resultItem.dataset.lat = venue.latitude || '';
                    resultItem.dataset.lng = venue.longitude || '';
                    
                    resultItem.addEventListener('click', function(e) {
                        e.preventDefault();
                        selectVenue(this.dataset);
                        venueSearchResults.classList.remove('show');
                        venueSearchInput.value = '';
                    });
                    
                    venueSearchResults.appendChild(resultItem);
                });
                
                venueSearchResults.classList.add('show');
            } else {
                const noResults = document.createElement('div');
                noResults.classList.add('dropdown-item', 'text-muted');
                noResults.textContent = 'No venues found. Try a different search or create a new venue.';
                venueSearchResults.appendChild(noResults);
                venueSearchResults.classList.add('show');
            }
        })
        .catch(error => {
            console.error('Error searching venues:', error);
            venueSearchResults.innerHTML = '';
            const errorItem = document.createElement('div');
            errorItem.classList.add('dropdown-item', 'text-danger');
            errorItem.textContent = 'Error searching venues. Please try again.';
            venueSearchResults.appendChild(errorItem);
            venueSearchResults.classList.add('show');
        });
    }, 300));
    
    // Function to select a venue (single selection only)
    window.selectVenue = function(venueData) {
        // Clear any existing selection first
        clearVenueSelection();
        
        // Set the new selection
        venueIdInput.value = venueData.id;
        locationInput.value = venueData.name;
        
        // Auto-populate address fields using standard field names
        const address1Field = document.getElementById('address1');
        const address2Field = document.getElementById('address2');
        const cityField = document.getElementById('city');
        const stateField = document.getElementById('state');
        const zipcodeField = document.getElementById('zipcode');
        
        if (address1Field) address1Field.value = venueData.address || '';
        if (address2Field) address2Field.value = ''; // Clear address2 as venue doesn't provide this
        if (cityField) cityField.value = venueData.city || '';
        if (stateField) stateField.value = venueData.state || '';
        if (zipcodeField) zipcodeField.value = venueData.zip || '';
        
        // Trigger change events for any listeners
        [address1Field, cityField, stateField, zipcodeField].forEach(field => {
            if (field) {
                field.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });
        
        selectedVenueName.textContent = venueData.name;
        selectedVenueAddress.textContent = venueData.city && venueData.state ? 
            `${venueData.city}, ${venueData.state}` : '';
        selectedVenueInfo.classList.remove('d-none');
        
        // Clear the search input to show selection is complete
        venueSearchInput.value = '';
        
        // Provide user feedback
        console.log('Venue selected:', venueData.name);
    }
    
    // Function to clear venue selection
    function clearVenueSelection() {
        venueIdInput.value = '';
        locationInput.value = '';
        selectedVenueInfo.classList.add('d-none');
        selectedVenueName.textContent = '';
        selectedVenueAddress.textContent = '';
        
        // Clear address fields
        const address1Field = document.getElementById('address1');
        const address2Field = document.getElementById('address2');
        const cityField = document.getElementById('city');
        const stateField = document.getElementById('state');
        const zipcodeField = document.getElementById('zipcode');
        
        if (address1Field) address1Field.value = '';
        if (address2Field) address2Field.value = '';
        if (cityField) cityField.value = '';
        if (stateField) stateField.value = '';
        if (zipcodeField) zipcodeField.value = '';
    }
    
    // Clear venue search
    if (clearVenueBtn) {
        clearVenueBtn.addEventListener('click', function() {
            venueSearchInput.value = '';
            venueSearchResults.innerHTML = '';
            venueSearchResults.classList.remove('show');
        });
    }
    
    // Remove selected venue
    if (removeVenueBtn) {
        removeVenueBtn.addEventListener('click', function() {
            clearVenueSelection();
            console.log('Venue selection cleared');
        });
    }
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!venueSearchInput.contains(e.target) && !venueSearchResults.contains(e.target)) {
            venueSearchResults.classList.remove('show');
        }
    });
}

function initializeClubSearch() {
    const clubSearchContainer = document.getElementById('club-search-container-club');
    if (clubSearchContainer) {
        window.clubSearchManager = new ClubSearchManager('club-search-container-club', {
            searchUrl: '<?php echo BASE_URL; ?>/calendar/searchClubs',
            createUrl: '<?php echo BASE_URL; ?>/calendar/createClubAjax',
            singleSelect: true, // Only allow one club selection for shows
            hiddenFieldName: 'club' // Use 'club' as the field name
        });
        
        // Load existing club data if available
        <?php if (!empty($data['club']) || (isset($show) && !empty($show->club))): ?>
        <?php $clubId = !empty($data['club']) ? $data['club'] : (isset($show->club) ? $show->club : ''); ?>
        // Try to get club details from the calendar system
        fetch('<?php echo BASE_URL; ?>/calendar/getClubById/<?php echo $clubId; ?>', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(clubData => {
            if (clubData && clubData.success && clubData.club) {
                window.clubSearchManager.setSelectedClubs([{
                    id: clubData.club.id,
                    name: clubData.club.name,
                    description: clubData.club.description || ''
                }]);
            }
        })
        .catch(error => {
            console.error('Error loading existing club data:', error);
        });
        <?php endif; ?>
    }
}

function initializeVenueModal() {
    // Initialize the venue modal
    const venueModal = new VenueModal({
        apiEndpoint: '<?php echo BASE_URL; ?>/calendar/createVenueApi',
        csrfToken: '<?php echo $_SESSION['csrf_token']; ?>',
        onVenueCreated: function(venue) {
            // Automatically select the newly created venue
            console.log('onVenueCreated callback called with venue:', venue);
            console.log('selectVenue function exists:', typeof window.selectVenue);
            
            if (typeof window.selectVenue === 'function') {
                window.selectVenue({
                    id: venue.id,
                    name: venue.name,
                    address: venue.address || '',
                    city: venue.city || '',
                    state: venue.state || '',
                    zip: venue.zip || '',
                    lat: venue.latitude || '',
                    lng: venue.longitude || ''
                });
            } else {
                console.error('selectVenue function is not available');
                // Fallback: just close the modal without selecting
                alert('Venue created successfully! Please refresh the page and select the venue manually.');
            }
        }
    });
    
    // Attach click event to the create venue button
    const createVenueBtn = document.getElementById('createVenueModalBtn');
    if (createVenueBtn) {
        createVenueBtn.addEventListener('click', function() {
            venueModal.open();
        });
    }
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>