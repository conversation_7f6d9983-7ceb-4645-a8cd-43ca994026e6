<?php 
// Ensure APPROOT is defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(dirname(dirname(__FILE__))));
}

// Make APPROOT available globally
if (!isset($GLOBALS['APPROOT'])) {
    $GLOBALS['APPROOT'] = APPROOT;
}

require APPROOT . '/views/includes/header.php'; 
?>

<style>
    /* Styles for clickable cards with button exceptions */
    .stretched-link-wrapper {
        position: static;
    }
    .stretched-link-wrapper::after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 0;
        content: "";
    }
    .z-index-1 {
        z-index: 1;
    }
</style>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <?php if (!empty($data['show']->banner_image)): ?>
            <div class="card mb-4">
                <img src="<?php echo BASE_URL; ?>/uploads/shows/<?php echo $data['show']->banner_image; ?>"
                     class="card-img-top" alt="<?php echo $data['show']->name; ?>">
            </div>
            <?php endif; ?>
            
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h5 class="card-title"><?php echo $data['show']->name; ?></h5>
                            <?php if (isset($data['show']->club) && !empty($data['show']->club)): ?>
                                <div class="mb-2">
                                    <small class="text-muted">Hosted by:</small>
                                    <span class="badge bg-info text-dark"><?php echo htmlspecialchars($data['show']->club); ?></span>
                                </div>
                            <?php endif; ?>
                            <?php if ($data['show']->fan_voting_enabled): ?>
                                <div class="badge badge-success mb-2">Fan Favorite Voting Enabled</div>
                            <?php endif; ?>
                        </div>
                        <div class="ms-3">
                            <?php
                            // Set notification button variables for car show
                            $event_id = $data['show']->id;
                            $event_type = 'car_show';
                            $button_class = 'btn-outline-primary btn-sm';
                            $show_text = true;
                            
                            // Include the notification button
                            include APPROOT . '/views/shared/notification_button.php';
                            ?>
                        </div>
                    </div>
                    <p class="card-text"><?php echo nl2br($data['show']->description); ?></p>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <!-- Show Dates -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    <strong class="text-primary">Show Times</strong>
                                </div>
                                <div class="ms-4">
                                    <div class="row g-1">
                                        <div class="col-12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">Starts:</small>
                                                <small class="fw-bold"><?php echo formatDateTimeForUser($data['show']->start_date, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?></small>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">Ends:</small>
                                                <small class="fw-bold"><?php echo formatDateTimeForUser($data['show']->end_date, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Registration Times -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-user-plus text-success me-2"></i>
                                    <strong class="text-success">Registration Times</strong>
                                </div>
                                <div class="ms-4">
                                    <div class="row g-1">
                                        <div class="col-12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">Opens:</small>
                                                <small class="fw-bold"><?php echo formatDateTimeForUser($data['show']->registration_start, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?></small>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">Closes:</small>
                                                <small class="fw-bold"><?php echo formatDateTimeForUser($data['show']->registration_end, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Times -->
                            <?php if (isset($data['show']->Judge_time) && !empty($data['show']->Judge_time)): ?>
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-1">
                                    <i class="fas fa-gavel text-warning me-2"></i>
                                    <strong class="text-warning">Judging Start</strong>
                                </div>
                                <div class="ms-4">
                                    <span class="fw-bold"><?php echo formatDateTimeForUser($data['show']->Judge_time, $_SESSION['user_id'] ?? null, 'g:i A'); ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php if (isset($data['show']->awards_time) && !empty($data['show']->awards_time)): ?>
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-1">
                                    <i class="fas fa-trophy text-warning me-2"></i>
                                    <strong class="text-warning">Awards Start</strong>
                                </div>
                                <div class="ms-4">
                                    <span class="fw-bold"><?php echo formatDateTimeForUser($data['show']->awards_time, $_SESSION['user_id'] ?? null, 'g:i A'); ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <!-- Location -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                    <strong class="text-dark">Location</strong>
                                </div>
                                <div class="ms-4">
                                    <span class="text-muted"><?php echo $data['show']->location; ?></span>
                                </div>
                            </div>

                            <!-- Coordinator -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-user-tie text-info me-2"></i>
                                    <strong class="text-dark">Coordinator</strong>
                                </div>
                                <div class="ms-4">
                                    <span class="text-muted"><?php echo isset($data['show']->coordinator_name) && $data['show']->coordinator_name ? $data['show']->coordinator_name : 'Not assigned'; ?></span>
                                </div>
                            </div>

                            <!-- Show Results -->
                            <?php if ($data['show']->status == 'completed'): ?>
                            <div class="mb-3">
                                <a href="<?php echo BASE_URL; ?>/show/results/<?php echo $data['show']->id; ?>" class="btn btn-success">
                                    <i class="fas fa-trophy me-2"></i>View Show Results
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if ($data['registration_open'] && isset($_SESSION['user_id'])): ?>
                        <?php if (!empty($data['user_registrations'])): ?>
                            <!-- Display registered vehicles -->
                            <div class="mb-3">
                                <h6 class="mb-3">Your Registered Vehicles:</h6>
                                <div class="row">
                                    <?php foreach ($data['user_registrations'] as $registration): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="card h-100 border-primary position-relative">
                                                <a href="<?php echo BASE_URL; ?>/user/registrations/view/<?php echo $registration->id; ?>" 
                                                   class="text-decoration-none text-dark stretched-link-wrapper">
                                                    <div class="card-body">
                                                        <div class="d-flex align-items-center">
                                                            <?php if (!empty($registration->primary_image)): ?>
                                                                <div class="me-3">
                                                                    <img src="<?php echo BASE_URL . '/' . $registration->primary_image; ?>" 
                                                                         alt="<?php echo $registration->year . ' ' . $registration->make . ' ' . $registration->model; ?>"
                                                                         class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                                </div>
                                                            <?php endif; ?>
                                                            <div>
                                                                <h6 class="card-title mb-1"><?php echo $registration->year . ' ' . $registration->make . ' ' . $registration->model; ?></h6>
                                                                <p class="card-text small text-muted mb-1">
                                                                    Category: <?php echo $registration->category_name; ?><br>
                                                                    Registered: <?php echo formatDateTimeForUser($registration->registration_date, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?>
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                                <div class="card-footer bg-transparent border-top-0">
                                                    <div class="d-flex gap-2">
                                                        <a href="<?php echo BASE_URL; ?>/user/registrations/view/<?php echo $registration->id; ?>" 
                                                           class="btn btn-sm btn-primary flex-grow-1 position-relative z-index-1">
                                                            <i class="fas fa-info-circle me-1"></i> View Details
                                                        </a>
                                                        <a href="<?php echo BASE_URL; ?>/shared/print_qr_codes/<?php echo $registration->id; ?>" 
                                                           class="btn btn-sm btn-outline-secondary position-relative z-index-1">
                                                            <i class="fas fa-qrcode"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <?php 
                                // Check if user has more vehicles to register
                                $registeredVehicleIds = array_map(function($reg) { return $reg->vehicle_id; }, $data['user_registrations']);
                                $hasMoreVehiclesToRegister = false;
                                
                                foreach ($data['vehicles'] as $vehicle) {
                                    if (!in_array($vehicle->id, $registeredVehicleIds)) {
                                        $hasMoreVehiclesToRegister = true;
                                        break;
                                    }
                                }
                                
                                if ($hasMoreVehiclesToRegister): 
                                ?>
                                    <a href="<?php echo BASE_URL; ?>/registration/register/<?php echo $data['show']->id; ?>" class="btn btn-primary mt-2">
                                        <i class="fas fa-plus-circle me-1"></i> Register Another Vehicle
                                    </a>
                                <?php else: ?>
                                    <div class="alert alert-info mt-2">
                                        <i class="fas fa-info-circle me-1"></i> All your vehicles are registered for this show.
                                        <a href="<?php echo BASE_URL; ?>/user/vehicles/add" class="alert-link">Add a new vehicle</a> to register more.
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <a href="<?php echo BASE_URL; ?>/registration/register/<?php echo $data['show']->id; ?>" class="btn btn-primary">Register for this Show</a>
                        <?php endif; ?>
                    <?php elseif ($data['registration_open'] && !isset($_SESSION['user_id'])): ?>
                        <a href="<?php echo BASE_URL; ?>/auth/login?redirect=registration/register/<?php echo $data['show']->id; ?>" class="btn btn-primary">Login to Register</a>
                    <?php elseif (!$data['registration_open']): ?>
                        <div class="alert alert-info">Registration is currently closed for this show.</div>
                    <?php endif; ?>
                    
                    <?php if ($data['show']->fan_voting_enabled && strtotime($data['show']->start_date) <= time() && strtotime($data['show']->end_date) >= time()): ?>
                        <div class="mt-3">
                            <a href="<?php echo BASE_URL; ?>/show/vote/<?php echo $data['show']->id; ?>" class="btn btn-success btn-lg">
                                <i class="fas fa-thumbs-up me-2"></i> Vote for Fan Favorites
                            </a>
                            <p class="text-muted mt-2">
                                <i class="fas fa-info-circle"></i> 
                                You can also scan QR codes at the show to vote for specific vehicles!
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Show Images Gallery -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Show Images</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($data['show_images']) && !empty($data['show_images'])): ?>
                        <?php 
                        // Find primary image
                        $primaryImage = null;
                        $otherImages = [];
                        
                        foreach ($data['show_images'] as $image) {
                            if (isset($image->is_primary) && $image->is_primary) {
                                $primaryImage = $image;
                            } else {
                                $otherImages[] = $image;
                            }
                        }
                        
                        // If no primary image is set, use the first image
                        if (!$primaryImage && count($data['show_images']) > 0) {
                            $primaryImage = $data['show_images'][0];
                        }
                        ?>
                        
                        <?php if ($primaryImage): ?>
                        <!-- Primary Image -->
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Featured Image</h6>
                            <div class="text-center">
                                <a href="<?php echo BASE_URL . '/' . $primaryImage->file_path; ?>" 
                                   data-image-viewer 
                                   data-gallery-id="show-gallery-<?php echo $data['show']->id; ?>"
                                   data-title="<?php echo $data['show']->name; ?> - Featured Image">
                                    <img src="<?php echo BASE_URL . '/' . $primaryImage->file_path; ?>"
                                         class="img-fluid rounded shadow"
                                         alt="<?php echo $data['show']->name; ?> - Featured Image"
                                         style="max-height: 400px; object-fit: contain;">
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (count($otherImages) > 0 || !$primaryImage): ?>
                        <!-- Other Images -->
                        <h6 class="text-muted mb-3">Additional Images</h6>
                        <div class="row">
                            <?php 
                            $imagesToShow = $otherImages;
                            if (!$primaryImage && count($data['show_images']) > 0) {
                                $imagesToShow = $data['show_images'];
                            }
                            
                            foreach ($imagesToShow as $index => $image): 
                            ?>
                                <div class="col-md-4 col-sm-6 mb-3">
                                    <a href="<?php echo BASE_URL . '/' . $image->file_path; ?>" 
                                       data-image-viewer 
                                       data-gallery-id="show-gallery-<?php echo $data['show']->id; ?>"
                                       data-title="<?php echo $data['show']->name; ?> - Image <?php echo $index + 1; ?>">
                                        <img src="<?php echo BASE_URL . '/' . $image->file_path; ?>"
                                             class="img-fluid rounded shadow-sm"
                                             alt="Show Image"
                                             style="transition: transform 0.2s; width: 100%; height: 200px; object-fit: cover;">
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (isset($_SESSION['user_role']) && ($_SESSION['user_role'] == 'admin' || ($_SESSION['user_role'] == 'coordinator' && $data['show']->coordinator_id == $_SESSION['user_id']))): ?>
                        <div class="mt-3">
                            <a href="<?php echo BASE_URL; ?>/image_editor/show/<?php echo $data['show']->id; ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-images me-1"></i> Manage Images
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <p class="text-muted">No additional images available for this show.</p>
                        
                        <?php if (isset($_SESSION['user_role']) && ($_SESSION['user_role'] == 'admin' || ($_SESSION['user_role'] == 'coordinator' && $data['show']->coordinator_id == $_SESSION['user_id']))): ?>
                        <div class="mt-3">
                            <a href="<?php echo BASE_URL; ?>/image_editor/show/<?php echo $data['show']->id; ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-images me-1"></i> Add Images
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Categories</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($data['categories'])): ?>
                        <p>No categories have been defined for this show yet.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Description</th>
                                        <th>Fee</th>
                                        <th>Entries</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['categories'] as $category): ?>
                                        <tr>
                                            <td><?php echo $category->name; ?></td>
                                            <td><?php echo $category->description; ?></td>
                                            <td>$<?php echo number_format($category->registration_fee, 2); ?></td>
                                            <td>
                                                <?php 
                                                    $count = isset($data['registration_counts'][$category->id]) ? $data['registration_counts'][$category->id] : 0;
                                                    $max = $category->max_entries > 0 ? $category->max_entries : '∞';
                                                    echo $count . ' / ' . $max;
                                                ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Show Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Status
                            <span class="badge bg-primary"><?php echo ucfirst($data['show']->status); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Fan Voting
                            <?php if ($data['show']->fan_voting_enabled): ?>
                                <span class="badge bg-success">Enabled</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Disabled</span>
                            <?php endif; ?>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Registration
                            <?php if ($data['registration_open']): ?>
                                <span class="badge bg-success">Open</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Closed</span>
                            <?php endif; ?>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Categories
                            <span class="badge bg-primary"><?php echo count($data['categories']); ?></span>
                        </li>

                        <!-- Show Times Section -->
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <strong class="text-primary">
                                    <i class="fas fa-calendar-alt me-1"></i> Show Times
                                </strong>
                            </div>
                            <div class="row g-2">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">Start:</small>
                                        <small class="fw-bold"><?php echo formatDateTimeForUser($data['show']->start_date, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?></small>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">End:</small>
                                        <small class="fw-bold"><?php echo formatDateTimeForUser($data['show']->end_date, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </li>

                        <!-- Registration Times Section -->
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <strong class="text-success">
                                    <i class="fas fa-user-plus me-1"></i> Registration Times
                                </strong>
                            </div>
                            <div class="row g-2">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">Opens:</small>
                                        <small class="fw-bold"><?php echo formatDateTimeForUser($data['show']->registration_start, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?></small>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">Closes:</small>
                                        <small class="fw-bold"><?php echo formatDateTimeForUser($data['show']->registration_end, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </li>

                        <?php if (isset($data['show']->Judge_time) && !empty($data['show']->Judge_time)): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-gavel me-1 text-warning"></i> Judging Start</span>
                            <span class="text-muted"><?php echo formatDateTimeForUser($data['show']->Judge_time, $_SESSION['user_id'] ?? null, 'g:i A'); ?></span>
                        </li>
                        <?php endif; ?>
                        <?php if (isset($data['show']->awards_time) && !empty($data['show']->awards_time)): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-trophy me-1 text-warning"></i> Awards Start</span>
                            <span class="text-muted"><?php echo formatDateTimeForUser($data['show']->awards_time, $_SESSION['user_id'] ?? null, 'g:i A'); ?></span>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- Map Section -->
            <?php if (isset($data['mapLocation']) && !empty($data['mapLocation']['address'])): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt text-danger me-2"></i>Location Map
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Map Container -->
                    <div id="showLocationMap" style="height: 300px; width: 100%; border-radius: 8px; overflow: hidden;" class="mb-3"></div>

                    <!-- Location Info -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-1"><?php echo htmlspecialchars($data['mapLocation']['name']); ?></h6>
                            <small class="text-muted"><?php echo htmlspecialchars($data['mapLocation']['address']); ?></small>
                        </div>
                        <div>
                            <a href="https://www.google.com/maps/search/?api=1&query=<?php echo urlencode($data['mapLocation']['address']); ?>"
                               target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i> Open in Maps
                            </a>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row g-2">
                        <!-- Directions Button -->
                        <?php if (isset($_SESSION['user_id']) && !empty($data['userAddress'])): ?>
                        <div class="col-12 col-md-6">
                            <button id="getDirectionsBtn" class="btn btn-success w-100">
                                <i class="fas fa-route me-2"></i>Get Directions
                            </button>
                            <div class="form-text mt-1 text-center">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    From: <?php
                                    $userAddressString = trim(implode(', ', array_filter([
                                        $data['userAddress']['address'],
                                        $data['userAddress']['city'],
                                        $data['userAddress']['state'],
                                        $data['userAddress']['zip']
                                    ])));
                                    echo htmlspecialchars($userAddressString);
                                    ?>
                                </small>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Calendar Map Button -->
                        <?php if (isset($_SESSION['user_id'])): ?>
                        <div class="col-12 <?php echo (isset($_SESSION['user_id']) && !empty($data['userAddress'])) ? 'col-md-6' : ''; ?>">
                            <a href="<?php echo BASE_URL; ?>/calendar/map?lat=<?php echo urlencode($data['mapLocation']['lat'] ?? ''); ?>&lng=<?php echo urlencode($data['mapLocation']['lng'] ?? ''); ?>&zoom=15&location=<?php echo urlencode($data['mapLocation']['address']); ?>&show_id=<?php echo urlencode($data['show']->id); ?>&show_name=<?php echo urlencode($data['show']->name); ?>"
                               class="btn btn-primary w-100">
                                <i class="fas fa-calendar-alt me-2"></i>View on Calendar Map
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- User Prompts for Non-Logged In or Missing Address -->
                    <?php if (!isset($_SESSION['user_id'])): ?>
                    <div class="text-center mt-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <a href="<?php echo BASE_URL; ?>/auth/login" class="alert-link">Login</a>
                            to get personalized directions and access the calendar map.
                        </div>
                    </div>
                    <?php elseif (empty($data['userAddress'])): ?>
                    <div class="text-center mt-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <a href="<?php echo BASE_URL; ?>/user/profile" class="alert-link">Add your address to your profile</a>
                            to get personalized directions to this show.
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin'): ?>
                <div class="card mb-4 border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Show Registrations</h5>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo BASE_URL; ?>/admin/registrations/<?php echo $data['show']->id; ?>" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-clipboard-list me-2"></i> Manage Show Registrations
                        </a>
                        <p class="text-muted mt-2 small">
                            <i class="fas fa-info-circle me-1"></i> Access the complete registration management for this show
                        </p>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['user_role']) && ($_SESSION['user_role'] == 'admin' || $_SESSION['user_role'] == 'coordinator')): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Admin Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <?php if ($_SESSION['user_role'] == 'admin'): ?>
                                <a href="<?php echo BASE_URL; ?>/admin/editShow/<?php echo $data['show']->id; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-edit me-2"></i> Edit Show
                                </a>
                                <a href="<?php echo BASE_URL; ?>/admin/editShow/<?php echo $data['show']->id; ?>#categories" class="list-group-item list-group-item-action">
                                    <i class="fas fa-tags me-2"></i> Manage Categories
                                </a>
                                <a href="<?php echo BASE_URL; ?>/admin/judgingMetrics/<?php echo $data['show']->id; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-chart-bar me-2"></i> Manage Judging Metrics
                                </a>
                                <a href="<?php echo BASE_URL; ?>/admin/ageWeights/<?php echo $data['show']->id; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-balance-scale me-2"></i> Manage Age Weights
                                </a>
                                <a href="<?php echo BASE_URL; ?>/admin/registrations/<?php echo $data['show']->id; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-clipboard-list me-2"></i> View Registrations
                                </a>
                                <a href="<?php echo BASE_URL; ?>/show/print_judge_qr/<?php echo $data['show']->id; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-qrcode me-2"></i> Print Judge QR Codes
                                </a>
                            <?php elseif ($_SESSION['user_role'] == 'coordinator' && $data['show']->coordinator_id == $_SESSION['user_id']): ?>
                                <a href="<?php echo BASE_URL; ?>/coordinator/editShow/<?php echo $data['show']->id; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-edit me-2"></i> Edit Show
                                </a>
                                <a href="<?php echo BASE_URL; ?>/coordinator/editShow/<?php echo $data['show']->id; ?>#categories" class="list-group-item list-group-item-action">
                                    <i class="fas fa-tags me-2"></i> Manage Categories
                                </a>
                                <a href="<?php echo BASE_URL; ?>/coordinator/registrations/<?php echo $data['show']->id; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-clipboard-list me-2"></i> View Registrations
                                </a>
                                <a href="<?php echo BASE_URL; ?>/show/print_judge_qr/<?php echo $data['show']->id; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-qrcode me-2"></i> Print Judge QR Codes
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    // Initialize image viewer when the page is fully loaded
    window.addEventListener('load', function() {
        console.log('Show view: Page fully loaded, initializing image viewer');
        if (typeof initImageViewer === 'function') {
            initImageViewer();
        } else {
            console.error('Show view: initImageViewer function not found');
        }
    });
    
    // Initialize notification button handlers
    function initNotificationHandlers() {
        const notificationBtn = document.querySelector('[data-notification-btn]');
        console.log('Show view: Looking for notification button:', notificationBtn);
        
        if (notificationBtn) {
            notificationBtn.addEventListener('click', function() {
                const eventId = this.getAttribute('data-event-id');
                const eventType = this.getAttribute('data-event-type');
                
                console.log('Show view: Notification button clicked - Event ID:', eventId, 'Event Type:', eventType);
                
                // Load notification modal content
                const url = '<?php echo BASE_URL; ?>/notification/subscriptionModal?event_id=' + eventId + '&event_type=' + eventType;
                console.log('Show view: Fetching URL:', url);
                
                fetch(url)
                    .then(response => {
                        console.log('Show view: Response received:', response);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Show view: JSON data:', data);
                        if (data.success) {
                            document.getElementById('notificationModalContent').innerHTML = data.html;
                            const modal = new bootstrap.Modal(document.getElementById('notificationModal'));
                            modal.show();
                        } else {
                            console.error('Show view: Server error:', data.message);
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Show view: Fetch error:', error);
                        alert('Error loading notification settings. Please try again.');
                    });
            });
        }
    }
    
    // Initialize notification handlers when page loads
    document.addEventListener('DOMContentLoaded', function() {
        initNotificationHandlers();
        initShowLocationMap();
    });

    // Initialize show location map
    function initShowLocationMap() {
        <?php if (isset($data['mapLocation']) && !empty($data['mapLocation']['address'])): ?>
        const mapContainer = document.getElementById('showLocationMap');
        if (!mapContainer) return;

        const mapProvider = '<?php echo $data['mapSettings']['provider'] ?? 'openstreetmap'; ?>';
        const apiKey = '<?php echo $data['mapSettings']['api_key'] ?? ''; ?>';
        const mapLocation = <?php echo json_encode($data['mapLocation']); ?>;
        const userAddress = <?php echo json_encode($data['userAddress'] ?? null); ?>;

        console.log('Show view: Initializing map with provider:', mapProvider);
        console.log('Show view: Map location:', mapLocation);

        // Initialize map based on provider
        switch (mapProvider) {
            case 'google':
                initGoogleMap(mapLocation, userAddress, apiKey);
                break;
            case 'mapbox':
                initMapboxMap(mapLocation, userAddress, apiKey);
                break;
            case 'here':
                initHereMap(mapLocation, userAddress, apiKey);
                break;
            case 'openstreetmap':
            default:
                initOpenStreetMap(mapLocation, userAddress);
                break;
        }

        // Initialize directions button
        const directionsBtn = document.getElementById('getDirectionsBtn');
        if (directionsBtn && userAddress) {
            directionsBtn.addEventListener('click', function() {
                getDirections(userAddress, mapLocation);
            });
        }
        <?php endif; ?>
    }

    // OpenStreetMap implementation
    function initOpenStreetMap(mapLocation, userAddress) {
        // Load Leaflet if not already loaded
        if (!window.L) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
            document.head.appendChild(link);

            const script = document.createElement('script');
            script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
            script.onload = function() {
                createOpenStreetMap(mapLocation, userAddress);
            };
            document.head.appendChild(script);
        } else {
            createOpenStreetMap(mapLocation, userAddress);
        }
    }

    function createOpenStreetMap(mapLocation, userAddress) {
        console.log('Creating OpenStreetMap with location:', mapLocation);

        // Parse coordinates as numbers and validate
        const lat = mapLocation.lat ? parseFloat(mapLocation.lat) : null;
        const lng = mapLocation.lng ? parseFloat(mapLocation.lng) : null;

        console.log('Parsed coordinates:', { lat, lng });

        // Validate coordinates
        const hasValidCoords = lat !== null && lng !== null &&
                              !isNaN(lat) && !isNaN(lng) &&
                              isFinite(lat) && isFinite(lng) &&
                              lat >= -90 && lat <= 90 &&
                              lng >= -180 && lng <= 180;

        // Use coordinates if available and valid, otherwise geocode
        if (hasValidCoords) {
            console.log('Using coordinates for map:', { lat, lng });
            const map = L.map('showLocationMap').setView([lat, lng], 15);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            L.marker([lat, lng])
                .addTo(map)
                .bindPopup(`<strong>${mapLocation.name}</strong><br>${mapLocation.address}`)
                .openPopup();
        } else {
            console.log('Geocoding address:', mapLocation.address);
            // Geocode the address
            geocodeAndShowMap(mapLocation, userAddress);
        }
    }

    function geocodeAndShowMap(mapLocation, userAddress) {
        const geocodeUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(mapLocation.address)}`;

        fetch(geocodeUrl)
            .then(response => response.json())
            .then(data => {
                if (data && data.length > 0) {
                    const result = data[0];
                    const lat = parseFloat(result.lat);
                    const lng = parseFloat(result.lon);

                    const map = L.map('showLocationMap').setView([lat, lng], 15);

                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(map);

                    L.marker([lat, lng])
                        .addTo(map)
                        .bindPopup(`<strong>${mapLocation.name}</strong><br>${mapLocation.address}`)
                        .openPopup();
                } else {
                    document.getElementById('showLocationMap').innerHTML =
                        '<div class="d-flex align-items-center justify-content-center h-100 text-muted">' +
                        '<i class="fas fa-map-marker-alt me-2"></i>Unable to load map for this location' +
                        '</div>';
                }
            })
            .catch(error => {
                console.error('Geocoding error:', error);
                document.getElementById('showLocationMap').innerHTML =
                    '<div class="d-flex align-items-center justify-content-center h-100 text-muted">' +
                    '<i class="fas fa-exclamation-triangle me-2"></i>Error loading map' +
                    '</div>';
            });
    }

    // Directions function
    function getDirections(userAddress, mapLocation) {
        const fromAddress = `${userAddress.address}, ${userAddress.city}, ${userAddress.state} ${userAddress.zip}`;
        const toAddress = mapLocation.address;

        // Open Google Maps directions in new tab
        const directionsUrl = `https://www.google.com/maps/dir/${encodeURIComponent(fromAddress)}/${encodeURIComponent(toAddress)}`;
        window.open(directionsUrl, '_blank');
    }

    // Google Maps implementation (if needed)
    function initGoogleMap(mapLocation, userAddress, apiKey) {
        if (!apiKey) {
            console.warn('Google Maps API key not provided, falling back to OpenStreetMap');
            initOpenStreetMap(mapLocation, userAddress);
            return;
        }

        // Store the callback data globally
        window.googleMapData = { mapLocation, userAddress };

        // Load Google Maps API with proper async loading and marker library
        if (!window.google) {
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&loading=async&libraries=marker&callback=createGoogleMapCallback`;
            script.async = true;
            script.defer = true;

            // Global callback function
            window.createGoogleMapCallback = function() {
                createGoogleMapInstance(window.googleMapData.mapLocation, window.googleMapData.userAddress);
            };

            document.head.appendChild(script);
        } else {
            createGoogleMapInstance(mapLocation, userAddress);
        }
    }

    function createGoogleMapInstance(mapLocation, userAddress) {
        console.log('Creating Google Map with location:', mapLocation);

        // Parse coordinates as numbers and validate
        const lat = mapLocation.lat ? parseFloat(mapLocation.lat) : null;
        const lng = mapLocation.lng ? parseFloat(mapLocation.lng) : null;

        console.log('Parsed coordinates:', { lat, lng });

        // Validate coordinates
        const hasValidCoords = lat !== null && lng !== null &&
                              !isNaN(lat) && !isNaN(lng) &&
                              isFinite(lat) && isFinite(lng) &&
                              lat >= -90 && lat <= 90 &&
                              lng >= -180 && lng <= 180;

        const mapOptions = {
            zoom: 15,
            center: hasValidCoords ?
                { lat: lat, lng: lng } :
                { lat: 39.8283, lng: -98.5795 }, // Default center (US)
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            mapId: 'show-location-map' // Required for AdvancedMarkerElement
        };

        const map = new google.maps.Map(document.getElementById('showLocationMap'), mapOptions);

        if (hasValidCoords) {
            console.log('Using coordinates for marker:', { lat, lng });
            createMarkerAtPosition(map, { lat, lng }, mapLocation);
        } else {
            console.log('Geocoding address:', mapLocation.address);
            // Geocode the address
            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({ address: mapLocation.address }, function(results, status) {
                if (status === 'OK' && results[0]) {
                    const location = results[0].geometry.location;
                    map.setCenter(location);
                    createMarkerAtPosition(map, location, mapLocation);
                } else {
                    console.error('Geocoding failed:', status);
                    // Show error message in map container
                    document.getElementById('showLocationMap').innerHTML =
                        '<div class="d-flex align-items-center justify-content-center h-100 text-muted">' +
                        '<div class="text-center">' +
                        '<i class="fas fa-exclamation-triangle mb-2 d-block"></i>' +
                        'Unable to load map for this location' +
                        '</div></div>';
                }
            });
        }
    }

    function createMarkerAtPosition(map, position, mapLocation) {
        try {
            // Try to use AdvancedMarkerElement (new recommended way)
            if (google.maps.marker && google.maps.marker.AdvancedMarkerElement) {
                const marker = new google.maps.marker.AdvancedMarkerElement({
                    map: map,
                    position: position,
                    title: mapLocation.name
                });

                const infoWindow = new google.maps.InfoWindow({
                    content: `<div style="padding: 8px;"><strong>${mapLocation.name}</strong><br>${mapLocation.address}</div>`
                });

                marker.addListener('click', function() {
                    infoWindow.open(map, marker);
                });

                // Open info window by default
                infoWindow.open(map, marker);

            } else {
                // Fallback to legacy Marker (with deprecation warning suppressed)
                const marker = new google.maps.Marker({
                    position: position,
                    map: map,
                    title: mapLocation.name
                });

                const infoWindow = new google.maps.InfoWindow({
                    content: `<div style="padding: 8px;"><strong>${mapLocation.name}</strong><br>${mapLocation.address}</div>`
                });

                marker.addListener('click', function() {
                    infoWindow.open(map, marker);
                });

                // Open info window by default
                infoWindow.open(map, marker);
            }
        } catch (error) {
            console.error('Error creating marker:', error);
        }
    }

    // Mapbox implementation (basic)
    function initMapboxMap(mapLocation, userAddress, apiKey) {
        if (!apiKey) {
            console.warn('Mapbox API key not provided, falling back to OpenStreetMap');
            initOpenStreetMap(mapLocation, userAddress);
            return;
        }

        // For now, fall back to OpenStreetMap
        // Full Mapbox implementation would require additional setup
        initOpenStreetMap(mapLocation, userAddress);
    }

    // HERE Maps implementation (basic)
    function initHereMap(mapLocation, userAddress, apiKey) {
        if (!apiKey) {
            console.warn('HERE Maps API key not provided, falling back to OpenStreetMap');
            initOpenStreetMap(mapLocation, userAddress);
            return;
        }

        // For now, fall back to OpenStreetMap
        // Full HERE Maps implementation would require additional setup
        initOpenStreetMap(mapLocation, userAddress);
    }
</script>

<!-- Notification Modal -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-labelledby="notificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" id="notificationModalContent">
            <!-- Content will be loaded dynamically -->
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>