<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Entity Template Assignment Tool</h1>
                <a href="javascript:history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back
                </a>
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">Assign Template to Entity</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3">
                                    <label for="entity_type" class="form-label">Entity Type</label>
                                    <select name="entity_type" id="entity_type" class="form-select" required>
                                        <option value="">Select Entity Type</option>
                                        <option value="show">Show</option>
                                        <option value="vehicle">Vehicle</option>
                                        <option value="registration">Registration</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="entity_selector" class="form-label">Entity Selector</label>
                                    <div class="input-group mb-2">
                                        <select id="entity_selector" class="form-select" disabled>
                                            <option value="">-- First select an entity type above --</option>
                                        </select>
                                        <button type="button" id="load_entities_btn" class="btn btn-outline-secondary" disabled>
                                            <i class="fas fa-sync-alt"></i> Load
                                        </button>
                                    </div>
                                    <small class="form-text text-muted">Select an entity from the dropdown or enter ID manually below</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="entity_id" class="form-label">Entity ID</label>
                                    <input type="number" name="entity_id" id="entity_id" class="form-control" required min="1">
                                    <small class="form-text text-muted">ID will be automatically filled when selecting from dropdown above</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="template_id" class="form-label">Template</label>
                                    <select name="template_id" id="template_id" class="form-select" required>
                                        <option value="">Select Template</option>
                                        <optgroup label="Event Templates">
                                            <?php foreach ($templatesByType['event'] ?? [] as $template): ?>
                                                <option value="<?php echo $template->id; ?>"><?php echo htmlspecialchars($template->name); ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                        <optgroup label="Vehicle Templates">
                                            <?php foreach ($templatesByType['vehicle'] ?? [] as $template): ?>
                                                <option value="<?php echo $template->id; ?>"><?php echo htmlspecialchars($template->name); ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                        <optgroup label="Show Templates">
                                            <?php foreach ($templatesByType['show'] ?? [] as $template): ?>
                                                <option value="<?php echo $template->id; ?>"><?php echo htmlspecialchars($template->name); ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                    </select>
                                </div>
                                
                                <div class="mb-3 d-flex justify-content-between">
                                    <input type="hidden" name="action" value="set">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i> Assign Template
                                    </button>
                                    <button type="button" id="revert_to_default_btn" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo me-2"></i> Revert to Default
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">Template Type Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <ul class="mb-0">
                                <li><strong>Shows</strong>: Use templates of type "event"</li>
                                <li><strong>Vehicles</strong>: Use templates of type "vehicle"</li>
                                <li><strong>Registrations</strong>: Use templates of type "show"</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">Current Template Assignments</h5>
                        </div>
                        <div class="card-body">
                            <ul class="nav nav-tabs" id="myTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="shows-tab" data-bs-toggle="tab" data-bs-target="#shows" type="button" role="tab" aria-controls="shows" aria-selected="true">Shows</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="vehicles-tab" data-bs-toggle="tab" data-bs-target="#vehicles" type="button" role="tab" aria-controls="vehicles" aria-selected="false">Vehicles</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="registrations-tab" data-bs-toggle="tab" data-bs-target="#registrations" type="button" role="tab" aria-controls="registrations" aria-selected="false">Registrations</button>
                                </li>
                            </ul>
                            <div class="tab-content pt-3" id="myTabContent">
                                <div class="tab-pane fade show active" id="shows" role="tabpanel" aria-labelledby="shows-tab">
                                    <?php if (empty($showsWithTemplates)): ?>
                                        <p class="text-muted">No shows with assigned templates.</p>
                                    <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>Name</th>
                                                        <th>Template</th>
                                                        <th>Type</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($showsWithTemplates as $show): ?>
                                                        <tr>
                                                            <td><?php echo $show->id; ?></td>
                                                            <td><?php echo htmlspecialchars($show->name); ?></td>
                                                            <td><?php echo htmlspecialchars($show->template_name ?? 'Unknown'); ?></td>
                                                            <td><?php echo htmlspecialchars($show->template_type ?? 'Unknown'); ?></td>
                                                            <td>
                                                                <button class="btn btn-sm btn-outline-secondary remove-btn" 
                                                                        data-entity-type="show" 
                                                                        data-entity-id="<?php echo $show->id; ?>"
                                                                        data-entity-name="<?php echo htmlspecialchars($show->name); ?>">
                                                                    <i class="fas fa-undo me-1"></i> Revert to Default
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="tab-pane fade" id="vehicles" role="tabpanel" aria-labelledby="vehicles-tab">
                                    <?php if (empty($vehiclesWithTemplates)): ?>
                                        <p class="text-muted">No vehicles with assigned templates.</p>
                                    <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>Vehicle</th>
                                                        <th>Template</th>
                                                        <th>Type</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($vehiclesWithTemplates as $vehicle): ?>
                                                        <tr>
                                                            <td><?php echo $vehicle->id; ?></td>
                                                            <td><?php echo htmlspecialchars($vehicle->name); ?></td>
                                                            <td><?php echo htmlspecialchars($vehicle->template_name ?? 'Unknown'); ?></td>
                                                            <td><?php echo htmlspecialchars($vehicle->template_type ?? 'Unknown'); ?></td>
                                                            <td>
                                                                <button class="btn btn-sm btn-outline-secondary remove-btn" 
                                                                        data-entity-type="vehicle" 
                                                                        data-entity-id="<?php echo $vehicle->id; ?>"
                                                                        data-entity-name="<?php echo htmlspecialchars($vehicle->name); ?>">
                                                                    <i class="fas fa-undo me-1"></i> Revert to Default
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="tab-pane fade" id="registrations" role="tabpanel" aria-labelledby="registrations-tab">
                                    <?php if (empty($registrationsWithTemplates)): ?>
                                        <p class="text-muted">No registrations with assigned templates.</p>
                                    <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>Registration</th>
                                                        <th>Template</th>
                                                        <th>Type</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($registrationsWithTemplates as $registration): ?>
                                                        <tr>
                                                            <td><?php echo $registration->id; ?></td>
                                                            <td><?php echo htmlspecialchars($registration->name); ?></td>
                                                            <td><?php echo htmlspecialchars($registration->template_name ?? 'Unknown'); ?></td>
                                                            <td><?php echo htmlspecialchars($registration->template_type ?? 'Unknown'); ?></td>
                                                            <td>
                                                                <button class="btn btn-sm btn-outline-secondary remove-btn" 
                                                                        data-entity-type="registration" 
                                                                        data-entity-id="<?php echo $registration->id; ?>"
                                                                        data-entity-name="<?php echo htmlspecialchars($registration->name); ?>">
                                                                    <i class="fas fa-undo me-1"></i> Revert to Default
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Template Confirmation Modal -->
<div class="modal fade" id="removeTemplateModal" tabindex="-1" aria-labelledby="removeTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="removeTemplateModalLabel">Confirm Template Removal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove the template from <span id="entity-name-display"></span>?</p>
                <p>This will revert the entity to using the default template.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="" id="remove-template-form">
                    <input type="hidden" name="entity_type" id="modal-entity-type">
                    <input type="hidden" name="entity_id" id="modal-entity-id">
                    <input type="hidden" name="action" value="remove">
                    <button type="submit" class="btn btn-danger">Remove Template</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Entity type change handler
        const entityTypeSelect = document.getElementById('entity_type');
        const entitySelector = document.getElementById('entity_selector');
        const entityIdInput = document.getElementById('entity_id');
        const loadEntitiesBtn = document.getElementById('load_entities_btn');
        const templateSelect = document.getElementById('template_id');
        const revertToDefaultBtn = document.getElementById('revert_to_default_btn');
        
        // Handle entity type change
        entityTypeSelect.addEventListener('change', function() {
            const entityType = this.value;
            
            // Reset entity selector and ID
            entitySelector.innerHTML = '<option value="">-- Select an entity --</option>';
            
            // Enable/disable entity selector and load button
            if (entityType) {
                entitySelector.disabled = false;
                loadEntitiesBtn.disabled = false;
            } else {
                entitySelector.disabled = true;
                loadEntitiesBtn.disabled = true;
            }
            
            // Show appropriate templates based on entity type selection
            const optgroups = templateSelect.querySelectorAll('optgroup');
            
            // Reset all options
            optgroups.forEach(function(optgroup) {
                optgroup.style.display = 'block';
            });
            
            // Show only relevant templates based on entity type
            if (entityType === 'show') {
                // Show only event templates
                optgroups.forEach(function(optgroup) {
                    if (optgroup.label !== 'Event Templates') {
                        optgroup.style.display = 'none';
                    }
                });
            } else if (entityType === 'vehicle') {
                // Show only vehicle templates
                optgroups.forEach(function(optgroup) {
                    if (optgroup.label !== 'Vehicle Templates') {
                        optgroup.style.display = 'none';
                    }
                });
            } else if (entityType === 'registration') {
                // Show only show templates
                optgroups.forEach(function(optgroup) {
                    if (optgroup.label !== 'Show Templates') {
                        optgroup.style.display = 'none';
                    }
                });
            }
        });
        
        // Load entities button handler
        loadEntitiesBtn.addEventListener('click', function() {
            const entityType = entityTypeSelect.value;
            if (!entityType) return;
            
            // Show loading state
            loadEntitiesBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
            loadEntitiesBtn.disabled = true;
            
            // Fetch entities via AJAX
            fetch(`${BASE_URL}/entityTemplates/getEntities/${entityType}`)
                .then(response => response.json())
                .then(data => {
                    // Reset selector
                    entitySelector.innerHTML = '<option value="">-- Select an entity --</option>';
                    
                    // Add entities to selector
                    if (data.entities && data.entities.length > 0) {
                        data.entities.forEach(entity => {
                            const option = document.createElement('option');
                            option.value = entity.id;
                            option.textContent = entity.name;
                            entitySelector.appendChild(option);
                        });
                    } else {
                        const option = document.createElement('option');
                        option.value = "";
                        option.textContent = "No entities found";
                        entitySelector.appendChild(option);
                    }
                    
                    // Reset button state
                    loadEntitiesBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Load';
                    loadEntitiesBtn.disabled = false;
                })
                .catch(error => {
                    console.error('Error fetching entities:', error);
                    // Reset button state
                    loadEntitiesBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Load';
                    loadEntitiesBtn.disabled = false;
                    
                    // Show error message
                    alert('Error loading entities. Please try again.');
                });
        });
        
        // Entity selector change handler
        entitySelector.addEventListener('change', function() {
            entityIdInput.value = this.value;
        });
        
        // Handle Remove Template button click (renamed to Revert to Default)
        revertToDefaultBtn.addEventListener('click', function() {
            const entityType = entityTypeSelect.value;
            const entityId = entityIdInput.value;
            
            if (!entityType || !entityId) {
                alert('Please select an entity type and enter an entity ID.');
                return;
            }
            
            document.getElementById('modal-entity-type').value = entityType;
            document.getElementById('modal-entity-id').value = entityId;
            document.getElementById('entity-name-display').textContent = entityType + ' #' + entityId;
            
            const removeModal = new bootstrap.Modal(document.getElementById('removeTemplateModal'));
            removeModal.show();
        });
        
        // Handle Remove buttons in the tables
        document.querySelectorAll('.remove-btn').forEach(function(button) {
            button.addEventListener('click', function() {
                const entityType = this.getAttribute('data-entity-type');
                const entityId = this.getAttribute('data-entity-id');
                const entityName = this.getAttribute('data-entity-name');
                
                document.getElementById('modal-entity-type').value = entityType;
                document.getElementById('modal-entity-id').value = entityId;
                document.getElementById('entity-name-display').textContent = entityType + ' #' + entityId + ' (' + entityName + ')';
                
                const removeModal = new bootstrap.Modal(document.getElementById('removeTemplateModal'));
                removeModal.show();
            });
        });
    });
</script>